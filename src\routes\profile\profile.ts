import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse } from '../../types';
import { UserModel } from '../../models/User';

export const get = {
    handler: async (req: AuthenticatedRequest, res: Response) => {
        try {
            if (!req.user) {
                const response: ApiResponse = {
                    success: false,
                    error: 'User not authenticated'
                };
                return res.status(401).json(response);
            }

            // Get user details from database
            res.status(200).json({
                success: true,
                data: { "hi": "there" },
                message: 'User profile retrieved successfully'
            });
        } catch (error: any) {
            const response: ApiResponse = {
                success: false,
                error: error.message || 'Failed to retrieve user profile'
            };

            res.status(500).json(response);
        }
    },
    config: {
        requireAuth: true
    }
}
